<?php

namespace App\Services\AI;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class OllamaService extends BaseAIService
{
    /**
     * URL base da API.
     *
     * @var string
     */
    protected string $apiUrl;

    /**
     * Construtor.
     *
     * @param array $config
     */
    public function __construct(array $config = [])
    {
        $this->serviceName = 'Ollama';
        $this->apiUrl = $config['api_url'] ?? env('OLLAMA_API_URL', 'http://localhost:11434/api');
        $this->defaultModel = $config['model'] ?? env('OLLAMA_DEFAULT_MODEL', 'llama2');

        $this->defaultOptions = [
            'model' => $this->defaultModel,
            'temperature' => 0.7,
            'num_predict' => 1000,
            'top_k' => 40,
            'top_p' => 0.9,
            'stream' => false,
        ];

        parent::__construct($config);
    }

    /**
     * {@inheritdoc}
     */
    public function isConfigured(): bool
    {
        // Ollama não precisa de API key, apenas verifica se a URL está configurada
        return !empty($this->apiUrl);
    }

    /**
     * Testa a conexão com o servidor Ollama.
     *
     * @return bool
     */
    public function testConnection(): bool
    {
        if (!$this->isConfigured()) {
            return false;
        }

        try {
            // Tentar fazer uma requisição simples para o endpoint de lista de modelos
            Log::info('Testando conexão com Ollama', ['url' => $this->apiUrl]);

            // Tentar vários endpoints possíveis
            $endpoints = [
                "{$this->apiUrl}/tags",
                str_replace('/api', '', $this->apiUrl) . '/api/tags',
                str_replace('/api', '', $this->apiUrl) . '/tags',
            ];

            foreach ($endpoints as $endpoint) {
                Log::info('Tentando endpoint', ['endpoint' => $endpoint]);
                $response = Http::timeout(5)->get($endpoint);

                if ($response->successful()) {
                    Log::info('Conexão com Ollama bem-sucedida', [
                        'endpoint' => $endpoint,
                        'status' => $response->status(),
                        'body' => $response->json()
                    ]);
                    return true;
                } else {
                    Log::warning('Falha ao conectar com Ollama', [
                        'endpoint' => $endpoint,
                        'status' => $response->status(),
                        'body' => $response->body()
                    ]);
                }
            }

            return false;
        } catch (Exception $e) {
            Log::error('Exceção ao testar conexão com Ollama', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * {@inheritdoc}
     */
    public function sendMessage(string $prompt, array $options = []): string
    {
        $messages = [
            ['role' => 'user', 'content' => $prompt]
        ];

        return $this->sendConversation($messages, $options);
    }

    /**
     * {@inheritdoc}
     */
    public function sendConversation(array $messages, array $options = []): string
    {
        if (!$this->isConfigured()) {
            return $this->formatErrorMessage('API URL not configured');
        }

        $options = $this->mergeOptions($options);

        // Converter formato de mensagens para o formato do Ollama
        $ollamaMessages = $this->convertToOllamaFormat($messages);

        try {
            $response = Http::post("{$this->apiUrl}/chat", [
                'model' => $options['model'],
                'messages' => $ollamaMessages,
                'options' => [
                    'temperature' => $options['temperature'],
                    'num_predict' => $options['num_predict'],
                    'top_k' => $options['top_k'],
                    'top_p' => $options['top_p'],
                ],
                'stream' => $options['stream'],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['message']['content'] ?? '';
            } else {
                $this->logError('API request failed', $response->body());
                return $this->formatErrorMessage('API request failed: ' . $response->body());
            }
        } catch (Exception $e) {
            $this->logError('Exception during API request', $e);
            return $this->formatErrorMessage('Exception during API request', $e);
        }
    }

    /**
     * {@inheritdoc}
     */
    public function generateEmbeddings(string $text): array
    {
        if (!$this->isConfigured()) {
            $this->logError('API URL not configured');
            return [];
        }

        try {
            $response = Http::post("{$this->apiUrl}/embeddings", [
                'model' => $this->defaultModel,
                'prompt' => $text,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['embedding'] ?? [];
            } else {
                $this->logError('Embeddings API request failed', $response->body());
                return [];
            }
        } catch (Exception $e) {
            $this->logError('Exception during embeddings API request', $e);
            return [];
        }
    }

    /**
     * Converte mensagens do formato OpenAI para o formato Ollama.
     *
     * @param array $messages
     * @return array
     */
    protected function convertToOllamaFormat(array $messages): array
    {
        $ollamaMessages = [];

        foreach ($messages as $message) {
            $role = $message['role'] ?? 'user';
            $content = $message['content'] ?? '';

            // Ollama usa os mesmos roles que OpenAI: 'system', 'user', 'assistant'
            $ollamaMessages[] = [
                'role' => $role,
                'content' => $content
            ];
        }

        return $ollamaMessages;
    }

    /**
     * Envia uma requisição para o endpoint de completions (para modelos que não suportam chat).
     *
     * @param string $prompt
     * @param array $options
     * @return string
     */
    public function generateCompletion(string $prompt, array $options = []): string
    {
        if (!$this->isConfigured()) {
            return $this->formatErrorMessage('API URL not configured');
        }

        $options = $this->mergeOptions($options);

        try {
            $response = Http::post("{$this->apiUrl}/generate", [
                'model' => $options['model'],
                'prompt' => $prompt,
                'options' => [
                    'temperature' => $options['temperature'],
                    'num_predict' => $options['num_predict'],
                    'top_k' => $options['top_k'],
                    'top_p' => $options['top_p'],
                ],
                'stream' => $options['stream'],
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return $data['response'] ?? '';
            } else {
                $this->logError('API request failed', $response->body());
                return $this->formatErrorMessage('API request failed: ' . $response->body());
            }
        } catch (Exception $e) {
            $this->logError('Exception during API request', $e);
            return $this->formatErrorMessage('Exception during API request', $e);
        }
    }

    /**
     * Obtém a lista de modelos disponíveis no servidor Ollama.
     *
     * @return array Retorna um array associativo no formato ['model_id' => 'Model Name']
     */
    public function getAvailableModels(): array
    {
        if (!$this->isConfigured()) {
            $this->logError('API URL not configured');
            return [];
        }

        // Testar a conexão primeiro
        $this->testConnection();

        try {
            // Tentar vários endpoints possíveis
            $endpoints = [
                "{$this->apiUrl}/tags",
                str_replace('/api', '', $this->apiUrl) . '/api/tags',
                str_replace('/api', '', $this->apiUrl) . '/tags',
            ];

            foreach ($endpoints as $endpoint) {
                Log::info('Tentando obter modelos do endpoint', ['endpoint' => $endpoint]);

                try {
                    $response = Http::timeout(5)->get($endpoint);

                    if ($response->successful()) {
                        $data = $response->json();
                        Log::info('Resposta da API Ollama', ['data' => $data]);

                        $models = [];

                        // Processar a resposta para extrair os modelos
                        if (isset($data['models']) && is_array($data['models'])) {
                            foreach ($data['models'] as $model) {
                                if (isset($model['name'])) {
                                    // Usar o nome como chave e valor
                                    $name = $model['name'];
                                    $displayName = $this->formatModelName($name);
                                    $models[$name] = $displayName;
                                }
                            }

                            Log::info('Modelos encontrados', ['count' => count($models), 'models' => $models]);
                            return $models;
                        }
                    }
                } catch (Exception $innerException) {
                    Log::warning('Erro ao tentar endpoint', [
                        'endpoint' => $endpoint,
                        'error' => $innerException->getMessage()
                    ]);
                    // Continuar para o próximo endpoint
                }
            }

            // Se chegou aqui, nenhum endpoint funcionou
            Log::error('Nenhum endpoint funcionou para obter modelos');

            // Retornar uma lista estática como fallback
            return [
                'llama2' => 'Llama 2',
                'mistral' => 'Mistral',
                'mixtral' => 'Mixtral',
                'phi' => 'Phi',
                'gemma' => 'Gemma',
                'codellama' => 'Code Llama',
            ];
        } catch (Exception $e) {
            $this->logError('Exception while getting models from Ollama', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Retornar uma lista estática como fallback
            return [
                'llama2' => 'Llama 2',
                'mistral' => 'Mistral',
                'mixtral' => 'Mixtral',
            ];
        }
    }

    /**
     * Formata o nome do modelo para exibição.
     *
     * @param string $modelName
     * @return string
     */
    protected function formatModelName(string $modelName): string
    {
        // Remover a tag :latest se existir
        $name = str_replace(':latest', '', $modelName);

        // Capitalizar a primeira letra de cada palavra
        $name = ucwords(str_replace(['-', '_', ':'], ' ', $name));

        // Se tiver uma versão (ex: llama2:13b), formatar adequadamente
        if (strpos($modelName, ':') !== false) {
            $parts = explode(':', $modelName);
            $baseName = ucwords(str_replace(['-', '_'], ' ', $parts[0]));
            $version = $parts[1];

            // Se não for 'latest', adicionar a versão
            if ($version !== 'latest') {
                return "{$baseName} ({$version})";
            }

            return $baseName;
        }

        return $name;
    }
}
