<?php

namespace App\Services\AI;

use Illuminate\Support\Facades\Log;

abstract class BaseAIService implements AIServiceInterface
{
    /**
     * Configurações padrão para o serviço.
     *
     * @var array
     */
    protected array $defaultOptions = [];

    /**
     * Nome do serviço.
     *
     * @var string
     */
    protected string $serviceName;

    /**
     * Chave da API.
     *
     * @var string|null
     */
    protected ?string $apiKey;

    /**
     * Modelo padrão a ser usado.
     *
     * @var string
     */
    protected string $defaultModel;

    /**
     * Construtor.
     *
     * @param array $config Configurações adicionais
     */
    public function __construct(array $config = [])
    {
        $this->defaultOptions = array_merge($this->defaultOptions, $config);
    }

    /**
     * {@inheritdoc}
     */
    public function getServiceName(): string
    {
        return $this->serviceName;
    }

    /**
     * {@inheritdoc}
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * <PERSON><PERSON><PERSON><PERSON> as opções fornecidas com as opções padrão.
     *
     * @param array $options
     * @return array
     */
    protected function mergeOptions(array $options): array
    {
        return array_merge($this->defaultOptions, $options);
    }

    /**
     * Formata uma mensagem de erro.
     *
     * @param string $message
     * @param mixed $exception
     * @return string
     */
    protected function formatErrorMessage(string $message, $exception = null): string
    {
        $errorMessage = "[{$this->serviceName} Error] {$message}";

        if ($exception && method_exists($exception, 'getMessage')) {
            $errorMessage .= ": " . $exception->getMessage();
        }

        return $errorMessage;
    }

    /**
     * Registra um erro no log.
     *
     * @param string $message
     * @param mixed $exception
     * @return void
     */
    protected function logError(string $message, $exception = null): void
    {
        $errorMessage = $this->formatErrorMessage($message, $exception);
        Log::error($errorMessage);
    }
}
